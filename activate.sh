#!/bin/bash
# 激活虚拟环境的便捷脚本
# 使用方法: source activate.sh 或 . activate.sh

if [ -f ".venv/bin/activate" ]; then
    source .venv/bin/activate
    echo "已激活虚拟环境 (.venv)"
    echo "当前Python: $(which python)"
    echo "当前Python版本: $(python --version)"
    echo ""
    echo "可用的开发工具:"
    echo "  - mypy: 类型检查"
    echo "  - pytest: 运行测试"
    echo "  - black: 代码格式化"
    echo "  - flake8: 代码风格检查"
    echo ""
    echo "退出虚拟环境: deactivate"
else
    echo "错误: 未找到虚拟环境！"
    echo "请先运行: uv venv && uv pip install -e \".[dev]\""
fi
